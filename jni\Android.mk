LOCAL_PATH := $(call my-dir)


# ========== ��ģ�� ==========
include $(CLEAR_VARS)
LOCAL_MODULE := wolfdfm.sh




# ===== ����ѡ���Ż� =====
LOCAL_CFLAGS += -D__ANDROID_API__=21
LOCAL_CFLAGS := -std=c17 -Os -s -fvisibility=hidden -w
LOCAL_CPPFLAGS := -std=c++17 -Os -s -fvisibility=hidden -fno-rtti -fno-exceptions -fmerge-all-constants
LOCAL_CPPFLAGS += -DVK_USE_PLATFORM_ANDROID_KHR
LOCAL_CPPFLAGS += -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES
LOCAL_CPPFLAGS += -DIMGUI_DISABLE_DEBUG_TOOLS
LOCAL_CPPFLAGS += -DIMGUI_ENABLE_FREETYPE
#-mllvm -sub

# ===== �������Ż�ѡ�� =====
LOCAL_LDFLAGS += -Wl,--strip-all -Wl,--gc-sections
LOCAL_LDFLAGS += -u AMediaDrm_createByUUID  # ǿ�����ӷ���


# ===== ͷ�ļ�·�� =====
LOCAL_C_INCLUDES += \
    $(LOCAL_PATH)/src \
    $(LOCAL_PATH)/include \
    $(LOCAL_PATH)/include/Fun \
    $(LOCAL_PATH)/include/Android_draw \
    $(LOCAL_PATH)/include/Android_Graphics \
    $(LOCAL_PATH)/include/Android_my_imgui \
    $(LOCAL_PATH)/include/Android_touch \
    $(LOCAL_PATH)/include/My_Utils \
    $(LOCAL_PATH)/include/ImGui \
    $(LOCAL_PATH)/include/ImGui/backends \
    $(LOCAL_PATH)/include/ImGui/misc/freetype \
    $(LOCAL_PATH)/include/ImGui/misc/git_freetype

# ===== Դ�ļ��б� =====
LOCAL_SRC_FILES := \
    src/main.cpp \
    src/Funs.cpp \
    src/Android_draw/draw_Gui.cpp \
    src/Android_touch/touch.cpp \
    src/Android_Graphics/GraphicsManager.cpp \
    src/Android_Graphics/OpenGLGraphics.cpp \
    src/Android_Graphics/VulkanGraphics.cpp \
    src/Android_Graphics/vulkan_wrapper.cpp \
    src/Android_my_imgui/AndroidImgui.cpp \
    src/Android_my_imgui/my_imgui.cpp \
    src/Android_my_imgui/my_imgui_impl_android.cpp \
    src/ImGui/imgui.cpp \
    src/ImGui/imgui_draw.cpp \
    src/ImGui/imgui_tables.cpp \
    src/ImGui/imgui_widgets.cpp \
    src/ImGui/backends/imgui_impl_android.cpp \
    src/ImGui/backends/imgui_impl_opengl3.cpp \
    src/ImGui/backends/imgui_impl_vulkan.cpp \
    src/ImGui/misc/freetype/imgui_freetype.cpp \
    src/My_Utils/stb_image.cpp

# ===== ���ӵ�ϵͳ�� =====
LOCAL_LDLIBS += -lmediandk
LOCAL_LDLIBS := -llog -landroid -lmediandk  # ��������
LOCAL_LDLIBS := -llog -landroid -lEGL -lGLESv3 -lz
LOCAL_STATIC_LIBRARIES := lib_git_freetype lib_driver


# ========== Ԥ���뾲̬��: freetype ==========
include $(CLEAR_VARS)
LOCAL_MODULE := lib_git_freetype
LOCAL_SRC_FILES := src/ImGui/misc/git_freetype/$(TARGET_ARCH_ABI)/libfreetype.a
include $(PREBUILT_STATIC_LIBRARY)

# ========== Ԥ���뾲̬��: driver ==========
include $(CLEAR_VARS)
LOCAL_MODULE := lib_driver
LOCAL_SRC_FILES := include/Fun/driver.a
include $(PREBUILT_STATIC_LIBRARY)
